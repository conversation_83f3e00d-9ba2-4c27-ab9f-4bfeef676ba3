<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>张三的简历</title>
<link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css">
<style>
:root {
--theme-color: #2B6CB0;
--base-font-size: 11pt;
--font-size: var(--base-font-size);
--spacing: 1.2;
--text-color: #333333;
--secondary-color: #666666;
}

@page {
size: A4;
margin: 0;
}

body {
margin: 0;
padding: 0;
}

/* 添加打印媒体查询和分页控制 */
@media print {
.resumeTemplateA01 {
    overflow: hidden;
    margin: 0;
    padding: 10mm;
}

.section {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 8mm;
    position: relative;
}

.title {
    break-after: avoid;
    page-break-after: avoid;
}

.edu-item,
.work-item,
.project-item,
.internship-item,
.info-wrapper,
.custom-header,
.school-item {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 4mm;
}

.description {
    orphans: 3;
    widows: 3;
}

.photo-wrapper,
.photo,
img {
    break-inside: avoid;
    page-break-inside: avoid;
}

.content {
    break-before: avoid;
    page-break-before: avoid;
}

table, ul, ol {
    break-inside: avoid;
    page-break-inside: avoid;
}
}

.resumeTemplateA01 {
position: relative;
width: 210mm;
min-height: 297mm;
margin: 0 auto;
padding: 10mm;
box-sizing: border-box;
font-size: var(--font-size);
line-height: var(--spacing);
color: var(--text-color);
page-break-after: always;
}

.resume-decoration-line {
content: '';
position: absolute;
left: 13mm;
top: calc(var(--base-font-size) + 4pt + 25mm);
bottom: 10mm;
width: 0.05mm;
background-color: var(--theme-color);
opacity: 0.8;
z-index: 1;
}

.resume-header {
margin-bottom: 5mm;
display: flex;
align-items: center;
margin-top: 2mm;
}

.icons-group {
display: flex;
gap: 3mm;
margin-left: 110mm;
margin-top: -21pt;
}

.resume-title {
font-size: 30pt;
font-weight: bold;
color: var(--theme-color);
margin-top: -25pt;
margin-bottom: 0pt;
margin-left: -2mm;
margin-right: 0pt;
}

.icon-circle {
width: 10mm;
height: 10mm;
border-radius: 50%;
background-color: var(--theme-color);
display: flex;
align-items: center;
justify-content: center;
}

.icon-circle svg {
width: 5mm;
height: 5mm;
}

.decoration-line {
width: 104%;
margin: -10mm -10mm 15mm -10mm;
padding: 0 10mm;
text-align: left;
margin-left: -23mm;
margin-top: -3mm;
}

.decoration-line svg {
width: 107%;
height: 10mm;
}

.section {
margin-bottom: 5mm;
padding: 3mm;
border-radius: 2mm;
margin-top: -10mm;
position: relative;
}

.section-basic-info {
margin-top: -18mm;
}

.basic-info-decoration {
width: 87%;
margin-top: -3.6mm;
margin-left: -12mm;
transform: scale(1.3);
transform-origin: left center;
color: var(--theme-color);
}

.basic-info-decoration svg {
width: 100%;
height: auto;
}

.header-decoration {
width: 87%;
margin-top: -3.6mm;
margin-left: -12mm;
transform: scale(1.3);
transform-origin: left center;
color: var(--theme-color);
}

.header-decoration svg {
width: 100%;
height: auto;
}


.title {
position: relative;
z-index: 2;
font-size: calc(var(--base-font-size));
line-height: 1;
color: #FFFFFF;
font-weight: bold;
padding: 2mm 0 2mm -2mm;
letter-spacing: 2pt;
}

.section-basic-info .title {
position: relative;
z-index: 2;
font-size: calc(var(--base-font-size));
line-height: 1;
color: #FFFFFF;
font-weight: bold;
padding: 2mm 0 2mm -2mm;
}

.content {
margin-bottom: 3mm;
margin-top: 3mm;
padding: 0 5mm;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color, #333333);
gap: 1mm;
display: flex;
flex-wrap: wrap;
letter-spacing: 1.2pt;
}

.degree, .time {
color: var(--secondary-color, #666666);
font-size: calc(var(--base-font-size) + 1pt);
line-height: var(--spacing);
font-weight: bold;
}

.courses {
color: var(--text-color, #333333);
margin-top: 0.5mm;
font-size: calc(var(--base-font-size) - 2pt);
line-height: var(--spacing);
}

.description {
color: var(--text-color, #333333);
margin-top: 0.5mm;
white-space: pre-wrap;
word-break: break-all;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
width: 100%;
}

.info-item {
margin-bottom: 1.5mm;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
}

.info-grid {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 2.5mm;
width: 100%;
}

.edu-item, .work-item, .internship-item, .project-item, .school-item {
margin-bottom: 3mm;
}

.time {
color: var(--text-color);
font-size: calc(var(--base-font-size) + 2pt);
margin-bottom: 1.5mm;
font-weight: bold;
}

.school, .company {
font-weight: bold;
margin-bottom: 1.5mm;
}

.major, .position {
color: var(--secondary-color);
margin-bottom: 1.5mm;
}

.info-wrapper {
display: flex;
justify-content: space-between;
align-items: flex-start;
width: 100%;
margin-bottom: 2.5mm;
}

.info-grid {
flex: 1;
display: grid;
grid-template-columns: 1fr 1fr;
gap: 2.5mm;
margin-right: 5mm;
}

.photo-wrapper {
flex-shrink: 0;
width: 35mm;
height: 45mm;
border: 0.35mm solid #ddd;
display: flex;
align-items: center;
justify-content: center;
background: #fff;
}

.photo {
max-width: 100%;
max-height: 100%;
object-fit: contain;
}

.info-item {
margin-bottom: 1.5mm;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
}

.info-item .label {
display: inline-block;
min-width: 12mm;
vertical-align: top;
}

.info-item .value {
display: inline-block;
word-break: break-word;
overflow-wrap: break-word;
max-width: calc(100% - 15mm);
}

/* 求职意向样式 */
.job-intention-content {
display: flex;
flex-direction: column;
gap: 2mm;
}

.job-intention-row {
display: flex;
gap: 10mm;
}

.job-intention-item {
flex: 1;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
}

.job-intention-label {
font-weight: bold;
margin-right: 2mm;
}

.job-intention-value {
color: var(--text-color);
}

/* 在校经历样式 */
.school-header {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 5mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.school-role, .school-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.school-role {
text-align: left;
}

.school-date {
text-align: left;
}

.school-content {
color: var(--text-color);
margin-top: 0.5mm;
white-space: pre-wrap;
word-break: break-all;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
width: 100%;
}

/* 教育经历样式 */
.education-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 5mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.school, .major-degree, .edu-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.school {
text-align: left;
}

.major-degree {
text-align: left;
}

.edu-date {
text-align: left;
}

.courses-label {
font-weight: bold;
}

/* 工作经历样式 */
.work-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 5mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.company, .position, .work-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.company {
text-align: left;
}

.position {
text-align: left;
}

.work-date {
text-align: left;
}

/* 实习经历样式 */
.internship-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 5mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.intern-company, .intern-position, .intern-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.intern-company {
text-align: left;
}

.intern-position {
text-align: left;
}

.intern-date {
text-align: left;
}

/* 项目经历样式 */
.project-header {
display: grid;
grid-template-columns: 1fr 1fr 1fr;
gap: 5mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.project-name, .project-role, .project-date {
font-weight: bold;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.project-name {
text-align: left;
}

.project-role {
text-align: left;
}

.project-date {
text-align: left;
}

/* 技能和获奖样式 */
.skill-item, .award-item, .interest-item {
margin-bottom: 1.5mm;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
}

/* 自定义模块样式 */
.custom-header {
display: grid;
grid-template-columns: 1fr 1fr;
gap: 5mm;
align-items: baseline;
margin-bottom: 2mm;
width: 100%;
min-height: 6mm;
}

.custom-name, .time {
font-weight: bold;
font-size: calc(var(--base-font-size) + 2pt);
line-height: var(--spacing);
color: var(--text-color);
display: block;
}

.custom-name {
text-align: left;
}

.time {
text-align: left;
}

/* 隐藏空模块 */
.hidden {
display: none;
}
</style>
</head>

<body>
<div class="resumeTemplateA01">
<div class="resume-decoration-line"></div>
<!-- 主标题 -->
<div class="resume-header">
    <div class="resume-title">个人简历</div>
    <div class="icons-group">
        <div class="icon-circle">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M946.2 553.66H766.9l0.35 73.56h-63.78l-0.35-73.56h-382l0.35 73.56h-63.78l-0.35-73.56H65v281.23a59.79 59.79 0 0 0 59.79 59.79h774.42A59.79 59.79 0 0 0 959 834.89V553.7q-6.4-0.03-12.8-0.04zM77.8 502.12h179.62l0.22-17.4a16 16 0 0 1 16-15.79h31.79a16 16 0 0 1 16 16.2l-0.21 17h381.83l0.22-17.4a16 16 0 0 1 16-15.79h31.78a16 16 0 0 1 16 16.2l-0.22 17h192V352.55a64 64 0 0 0-63.78-63.78H767.54V193.1a64 64 0 0 0-63.78-63.78H320.24a64 64 0 0 0-63.78 63.78v95.67H128.9a64 64 0 0 0-63.78 63.78v149.53q6.34 0.03 12.68 0.04z m243.27-308.91l0.11-0.11h381.64l0.11 0.11v95.56H321.07z" fill="#ffffff"></path>
            </svg>
        </div>
        <div class="icon-circle">
            <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M466.090667 148.181333a86.485333 86.485333 0 0 1 78.848 0.768L951.466667 362.026667a17.28 17.28 0 0 1-0.298667 30.805333l-405.76 203.093333a86.485333 86.485333 0 0 1-78.890667-0.768L60.032 382.037333a17.28 17.28 0 0 1 0.256-30.805333l405.802667-203.050667z M211.2 502.314667v193.109333c0 6.570667 3.712 12.544 9.557333 15.488l266.154667 132.992c11.861333 5.973333 25.813333 6.101333 37.845333 0.426667l281.856-133.546667a17.28 17.28 0 0 0 9.898667-15.616v-191.786667l-310.784 155.392-294.528-156.458666z" fill="#ffffff"></path>
            </svg>
        </div>
        <div class="icon-circle">
            <svg viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="200" height="200">
                <path d="M1018.319924 112.117535q4.093748 9.210934 6.652341 21.492179t2.558593 25.585928-5.117186 26.609365-16.374994 25.585928q-12.281245 12.281245-22.003898 21.492179t-16.886712 16.374994q-8.187497 8.187497-15.351557 14.32812l-191.382739-191.382739q12.281245-11.257808 29.167958-27.121083t28.144521-25.074209q14.32812-11.257808 29.679676-15.863275t30.191395-4.093748 28.656239 4.605467 24.050772 9.210934q21.492179 11.257808 47.589826 39.402329t40.425766 58.847634zM221.062416 611.554845q6.140623-6.140623 28.656239-29.167958t56.289041-56.80076l74.710909-74.710909 82.898406-82.898406 220.038979-220.038979 191.382739 192.406177-220.038979 220.038979-81.874969 82.898406q-40.937484 39.914047-73.687472 73.175753t-54.242167 54.753885-25.585928 24.562491q-10.234371 9.210934-23.539054 19.445305t-27.632802 16.374994q-14.32812 7.16406-41.960921 17.398431t-57.824197 19.957024-57.312478 16.886712-40.425766 9.210934q-27.632802 3.070311-36.843736-8.187497t-5.117186-37.867173q2.046874-14.32812 9.722653-41.449203t16.374994-56.289041 16.886712-53.730448 13.304682-33.773425q6.140623-14.32812 13.816401-26.097646t22.003898-26.097646z" fill="#ffffff"></path>
            </svg>
        </div>
    </div>
</div>
<!-- 装饰线 -->
<div class="decoration-line">
    <svg width="2000" height="25" xmlns="http://www.w3.org/2000/svg">
        <g>
            <rect stroke="#AAAAAA" id="svg_4" height="8" width="900" y="9.5" x="427" fill="#AAAAAA"/>
            <path stroke="var(--theme-color)" id="svg_5" d="m10,5l417,0l0,13l-417,0l0,-13z" fill="var(--theme-color)"/>
            <path stroke="var(--theme-color)" id="svg_6" d="m427,18l0,-13l11.4,13l-11.4,0z" fill="var(--theme-color)"/>
        </g>
    </svg>
</div>




<svg style="display: none;">
  <symbol id="svg-header-decoration" width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet"
    xmlns="http://www.w3.org/2000/svg">
    <g style="z-index: 5;">
      <title>Layer 1</title>
      <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999"
        fill="var(--theme-color)" style="z-index: 5;" />
      <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z"
        opacity="undefined" fill="var(--theme-color)" style="z-index: 5;" />
      <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z"
        fill="var(--theme-color)" style="z-index: 5;" />
      <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443"
        fill="var(--theme-color)" style="z-index: 5;" />
      <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443"
        fill="var(--theme-color)" style="z-index: 5;" />
      <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618"
        fill="var(--theme-color)" style="z-index: 5;" />
      <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11"
        d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)"
        style="z-index: 5;" />
    </g>
  </symbol>

</svg>























<!-- 根据moduleOrders排序显示各个模块 -->

  <!-- 基本信息 -->
  
  <div class="section section-basic-info">
      <div class="title">基本信息</div>
      <div class="basic-info-header">
          <svg class="header-decoration">
            <use xlink:href="#svg-header-decoration"></use>
          </svg>
          
      </div>
      <div class="content">
          <div class="info-wrapper">
              <div class="info-grid">
                  <div class="info-item"><span class="label">姓名：</span><span class="value">张三</span></div>
                  <div class="info-item"><span class="label">性别：</span><span class="value">男</span></div>
                  <div class="info-item"><span class="label">年龄：</span><span class="value">30</span></div>
                  <div class="info-item"><span class="label">电话：</span><span class="value">1231313123</span></div>
                  <div class="info-item"><span class="label">邮箱：</span><span class="value"><EMAIL></span></div>
                  
                  
                  <div class="info-item"><span class="label">婚姻：</span><span class="value">未婚</span></div>
                  <div class="info-item"><span class="label">政治面貌：</span><span class="value">群众</span></div>
                  <div class="info-item"><span class="label">民族：</span><span class="value">汉</span></div>
                  <div class="info-item"><span class="label">籍贯：</span><span class="value">浙江</span></div>
                  <div class="info-item"><span class="label">身高：</span><span class="value">180</span></div>
                  <div class="info-item"><span class="label">体重：</span><span class="value">80</span></div>
                  <div class="info-item"><span class="label">微信：</span><span class="value">weixinhao</span></div>
              </div>
              
              <div class="photo-wrapper">
                  <img src="data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAe0AAAOYCAYAAAD4xKugAAAAAXNSR0IArs4c6QAAIABJREFUeF7svXm05GdZ7/2200%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2275%22%20y%3D%22100%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'" />
              </div>
              
          </div>
      </div>
  </div>
  

  
  <div class="section">
      <div class="title">求职意向</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content job-intention-content">
          <div class="job-intention-row">
              <div class="job-intention-item"><span class="job-intention-label">期望职位：</span><span class="job-intention-value">总裁</span></div>
              <div class="job-intention-item"><span class="job-intention-label">期望薪资：</span><span class="job-intention-value">5-7k</span></div>
          </div>
          <div class="job-intention-row">
              <div class="job-intention-item"><span class="job-intention-label">期望城市：</span><span class="job-intention-value">上海</span></div>
              <div class="job-intention-item"><span class="job-intention-label">求职状态：</span><span class="job-intention-value">在职</span></div>
          </div>
      </div>
  </div>
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  
  <div class="section">
      <div class="title">教育经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="edu-item">
              <div class="education-header">
                  <div class="school">上海交通大学</div>
                  <div class="major-degree">会计学 / 本科</div>
                  <div class="edu-date">2025-05 - 2025-05</div>
              </div>
              
          </div>
          
          <div class="edu-item">
              <div class="education-header">
                  <div class="school">上海立信会计学院。</div>
                  <div class="major-degree">会计学，管理学 组织学管理 / 本科</div>
                  <div class="edu-date">2025-06 - 2025-06</div>
              </div>
              
          </div>
          
          <div class="edu-item">
              <div class="education-header">
                  <div class="school">上海复旦大学</div>
                  <div class="major-degree">新闻学管理 / 本科</div>
                  <div class="edu-date">2025-06 - 2025-06</div>
              </div>
              
          </div>
          
      </div>
  </div>
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  
  <div class="section">
      <div class="title">在校经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="school-item">
              <div class="school-header">
                  <div class="school-role">老师</div>
                  <div class="school-date">2025-05 - 2025-06</div>
              </div>
              <div class="school-content">&lt;p&gt;毕业后担任助教。帮助博士导师进行学生管理。&lt;/p&gt;</div>
          </div>
          
          <div class="school-item">
              <div class="school-header">
                  <div class="school-role">教务处实习生</div>
                  <div class="school-date">2025-06 - </div>
              </div>
              <div class="school-content">&lt;p&gt;帮助新生同学。很好的融入学生团体。&lt;/p&gt;&lt;p&gt;帮助新生注册登记&lt;/p&gt;</div>
          </div>
          
      </div>
  </div>
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  
  <div class="section">
      <div class="title">实习经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="internship-item">
              <div class="internship-header">
                  <div class="intern-company">sdflk胜多负少</div>
                  <div class="intern-position">收到</div>
                  <div class="intern-date">2025-05 - 2025-05</div>
              </div>
              
          </div>
          
      </div>
  </div>
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  
  <div class="section">
      <div class="title">工作经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="work-item">
              <div class="work-header">
                  <div class="company">上海瑞通资产管理有限</div>
                  <div class="position">金融市场分析师</div>
                  <div class="work-date">2025-05 - 2025-05</div>
              </div>
              <div class="description">帮助老板收集数据。整理资料，打印资料。帮助各部门的投资经理分析市场数据。制定投资计划。调研上市公司等等内容。</div>
          </div>
          
          <div class="work-item">
              <div class="work-header">
                  <div class="company">上海证券有限责任公司。</div>
                  <div class="position">投资顾问</div>
                  <div class="work-date">2025-06 - 2025-06</div>
              </div>
              <div class="description">差投资市场的动态变化。关注基本面信息。收集各项上市公司的数据。啊，总结景区市场发生的一些变化来针对性的对客户未来的投资理财计划进行分析帮助客户对相关公司上市公司的了解。进行拓展。
如果有客户对相关上市公司感兴趣的话，我们会联系相关的上市公司进行调研。</div>
          </div>
          
      </div>
  </div>
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  
  <div class="section">
      <div class="title">项目经历</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="project-item">
              <div class="project-header">
                  <div class="project-name">是</div>
                  <div class="project-role">上市</div>
                  <div class="project-date">2025-05 - 2025-05</div>
              </div>
              <div class="description">是</div>
          </div>
          
      </div>
  </div>
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  
  <div class="section">
      <div class="title">技能特长</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="skill-item">是是是</div>
          
      </div>
  </div>
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  
  <div class="section">
      <div class="title">获奖证书</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="award-item">第三方士大夫</div>
          
      </div>
  </div>
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  
  <div class="section">
      <div class="title">兴趣爱好</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="interest-item">呵呵哈哈哈</div>
          
      </div>
  </div>
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  
  <div class="section">
      <div class="title">自我评价</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="description">士大夫撒啊是是

s的方式
士大夫
sdf sdf wewerw werw

沃尔沃二人
沃尔沃二</div>
          
      </div>
  </div>
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  
  <div class="section">
      <div class="title">1111111</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="custom-header">
              <div class="custom-name">角色1</div>
              <div class="time">2025-05-30 - 2025-05-30</div>
          </div>
          <div class="description">哈河 内容1</div>
          
      </div>
  </div>
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  
  <div class="section">
      <div class="title">22222222</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="custom-header">
              <div class="custom-name">角色2</div>
              <div class="time">2025-05-30 - 2025-05-30</div>
          </div>
          <div class="description">22222</div>
          
      </div>
  </div>
  

  <!-- 自定义模块3 -->
  

  <!-- 基本信息 -->
  

  <!-- 求职意向 -->
  

  <!-- 在校经历 -->
  

  <!-- 教育经历 -->
  

  <!-- 工作经历 -->
  

  <!-- 实习经历 -->
  

  <!-- 项目经历 -->
  

  <!-- 技能特长 -->
  

  <!-- 获奖证书 -->
  

  <!-- 自我评价 -->
  

  <!-- 兴趣爱好 -->
  

  <!-- 自定义模块1 -->
  

  <!-- 自定义模块2 -->
  

  <!-- 自定义模块3 -->
  
  <div class="section">
      <div class="title">3333333</div>
      <div class="basic-info-decoration">
          <svg width="900" height="38" viewBox="0 0 900 38" preserveAspectRatio="xMinYMid meet" xmlns="http://www.w3.org/2000/svg">
              <g style="z-index: 5;">
                  <title>Layer 1</title>
                  <rect stroke="var(--theme-color)" id="svg_1" height="23.84746" width="101.96588" y="0" x="38.59999" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_3" d="m38.77418,23.9044l810,0l0,0.14772l-810,0l0,-0.14772z" opacity="undefined" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path stroke="var(--theme-color)" id="svg_4" d="m140.14988,23.06247l0,-23.59034l25.28527,23.59034l-25.28527,0z" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_6" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_8" y2="23.28803" x2="176.94165" y1="0" x1="149.7443" fill="var(--theme-color)" style="z-index: 5;"/>
                  <line stroke="var(--theme-color)" id="svg_9" y2="23.03322" x2="186.26674" y1="0" x1="160.39618" fill="var(--theme-color)" style="z-index: 5;"/>
                  <path transform="rotate(-180 46.2005 30.219)" stroke="var(--theme-color)" id="svg_11" d="m39.93603,35.41166l0,-10.38445l12.52887,10.38445l-12.52887,0z" fill="var(--theme-color)" style="z-index: 5;"/>
              </g>
          </svg>
      </div>
      <div class="content">
          
          <div class="custom-header">
              <div class="custom-name">23333233</div>
              <div class="time">2025-05-30 - 2025-05-30</div>
          </div>
          
          
      </div>
  </div>
  


</body>
</html>